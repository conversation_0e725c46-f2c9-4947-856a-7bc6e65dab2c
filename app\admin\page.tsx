"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Users,
  ShoppingBag,
  CreditCard,
  DollarSign,
  Package,
  AlertTriangle,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import AdminDashboardChart from "@/components/admin/AdminDashboardChart";

// Mock data for dashboard
const mockStats = {
  totalUsers: 256,
  totalProducts: 124,
  totalOrders: 89,
  totalRevenue: 12589.99,
  salesMonths: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
  salesData: [120, 190, 300, 500, 200, 300],
  categoryLabels: ["Skincare", "Makeup", "Haircare", "Fragrance"],
  categoryData: [300, 50, 100, 80],
  orderStatusLabels: ["Completed", "Pending", "Cancelled"],
  orderStatusData: [200, 80, 20],
  recentOrders: [
    {
      id: "ORD-1234",
      customer: "<PERSON> Doe",
      date: "2023-05-15",
      status: "delivered",
      total: 249.99,
    },
    {
      id: "ORD-5678",
      customer: "<PERSON>",
      date: "2023-05-14",
      status: "processing",
      total: 159.99,
    },
    {
      id: "ORD-9012",
      customer: "Robert Johnson",
      date: "2023-05-13",
      status: "shipped",
      total: 349.99,
    },
    {
      id: "ORD-3456",
      customer: "Emily Davis",
      date: "2023-05-12",
      status: "delivered",
      total: 99.99,
    },
  ],
  lowStockProducts: [
    {
      id: "PROD-123",
      name: "Wireless Bluetooth Headphones",
      stock: 3,
      threshold: 5,
    },
    {
      id: "PROD-456",
      name: "Premium Yoga Mat",
      stock: 2,
      threshold: 10,
    },
    {
      id: "PROD-789",
      name: "Stainless Steel Water Bottle",
      stock: 4,
      threshold: 10,
    },
  ],
};

export default function AdminDashboardPage() {
  const [stats, setStats] = useState(mockStats);

  // In real app, fetch stats from API here
  // useEffect(() => {
  //   fetch('/api/admin/stats').then(...)
  // }, [])

  // Function to render status badge
  const renderStatusBadge = (status: string) => {
    const statusClasses = {
      delivered: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
      processing: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
      shipped: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
      cancelled: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
    };

    const statusClass = statusClasses[status as keyof typeof statusClasses] || "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusClass}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  return (
    <div className="space-y-8">
      {/* Charts Section - integrated with stats */}
      <AdminDashboardChart stats={stats} />

      {/* Recent Orders Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4">Recent Orders</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
              <tr>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                  Order ID
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                  Customer
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                  Date
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                  Status
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                  Total
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {stats.recentOrders.map((order) => (
                <tr key={order.id}>
                  <td className="px-4 py-2 whitespace-nowrap">{order.id}</td>
                  <td className="px-4 py-2 whitespace-nowrap">{order.customer}</td>
                  <td className="px-4 py-2 whitespace-nowrap">{order.date}</td>
                  <td className="px-4 py-2 whitespace-nowrap">
                    <span
                      className={`px-2 py-1 rounded text-xs font-semibold ${
                        order.status === "delivered"
                          ? "bg-green-100 text-green-800"
                          : order.status === "processing"
                          ? "bg-yellow-100 text-yellow-800"
                          : order.status === "shipped"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {order.status}
                    </span>
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap">
                    ${order.total.toFixed(2)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
