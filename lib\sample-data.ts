export const sampleProducts = [
  {
    name: 'Wireless Bluetooth Headphones',
    description: 'Premium noise-cancelling wireless headphones with crystal clear sound and long battery life.',
    price: 199.99,
    image: 'https://images.pexels.com/photos/3394650/pexels-photo-3394650.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'electronics',
  },
  {
    name: 'Slim Fit Men\'s Dress Shirt',
    description: 'Elegant slim-fit men\'s dress shirt made with premium cotton for comfort and style.',
    price: 59.99,
    image: 'https://images.pexels.com/photos/297933/pexels-photo-297933.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'clothing',
  },
  {
    name: 'Modern Coffee Table',
    description: 'Stylish mid-century inspired coffee table with solid wood legs and glass top.',
    price: 249.99,
    image: 'https://images.pexels.com/photos/1866149/pexels-photo-1866149.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'home',
  },
  {
    name: 'Professional Camera Kit',
    description: 'Complete professional camera kit with multiple lenses, tripod, and carrying case.',
    price: 1299.99,
    image: 'https://images.pexels.com/photos/51383/photo-camera-subject-photographer-51383.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'electronics',
  },
  {
    name: 'Luxury Watch Collection',
    description: 'Elegant luxury watch with premium materials and sophisticated design.',
    price: 349.99,
    image: 'https://images.pexels.com/photos/190819/pexels-photo-190819.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'clothing',
  },
  {
    name: 'Premium Yoga Mat',
    description: 'Extra thick eco-friendly yoga mat with excellent grip and carrying strap.',
    price: 79.99,
    image: 'https://images.pexels.com/photos/4498362/pexels-photo-4498362.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'beauty',
  },
  {
    name: 'Stainless Steel Cookware Set',
    description: 'Professional-grade stainless steel cookware set with 12 pieces for all your cooking needs.',
    price: 399.99,
    image: 'https://images.pexels.com/photos/932090/pexels-photo-932090.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'home',
  },
  {
    name: 'Bestselling Novel Collection',
    description: 'Collection of award-winning novels from this year\'s bestseller list.',
    price: 49.99,
    image: 'https://images.pexels.com/photos/1130980/pexels-photo-1130980.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'books',
  },
  {
    name: 'Smart Home Speaker',
    description: 'Voice-controlled smart speaker with premium sound quality and home automation features.',
    price: 129.99,
    image: 'https://images.pexels.com/photos/1109099/pexels-photo-1109099.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'electronics',
  },
  {
    name: 'Organic Skincare Set',
    description: 'Complete organic skincare set with cleansers, moisturizers, and serums for all skin types.',
    price: 89.99,
    image: 'https://images.pexels.com/photos/725998/pexels-photo-725998.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'beauty',
  },
  {
    name: 'Designer Sunglasses',
    description: 'Premium designer sunglasses with UV protection and stylish frames.',
    price: 159.99,
    image: 'https://images.pexels.com/photos/701877/pexels-photo-701877.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'clothing',
  },
  {
    name: 'Ergonomic Office Chair',
    description: 'High-quality ergonomic office chair with lumbar support and adjustable features.',
    price: 299.99,
    image: 'https://images.pexels.com/photos/1957477/pexels-photo-1957477.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'home',
  },
];